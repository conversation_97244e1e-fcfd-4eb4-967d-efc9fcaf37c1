const ul = document.getElementById('history');
const btnClear = document.getElementById('clear-logs');

function renderLogs() {
  ul.innerHTML = '';
  chrome.storage.local.get('elcutLogs', res => {
    const logs = res.elcutLogs || [];
    if (logs.length === 0) {
      const li = document.createElement('li');
      li.textContent = '— No history yet —';
      ul.append(li);
    } else {
      logs.forEach(line => {
        const li = document.createElement('li');
        li.textContent = line;
        ul.append(li);
      });
    }
  });
}

btnClear.addEventListener('click', () => {
  chrome.storage.local.set({ elcutLogs: [] }, renderLogs);
});

renderLogs();
