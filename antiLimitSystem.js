// ═══════════════════════════════════════════════════════════════════════════════
// SMART ANTI-LIMIT MONITORING SYSTEM FOR EL-CUT EXTENSION
// ═══════════════════════════════════════════════════════════════════════════════
// This module provides intelligent API rate limit monitoring, prediction, and 
// automatic switching between primary and fallback APIs to minimize disruption.
// ═══════════════════════════════════════════════════════════════════════════════

class SmartAntiLimitSystem {
  constructor() {
    // Core monitoring data
    this.apiEndpoints = new Map(); // Track all API endpoints
    this.rateLimitHistory = []; // Historical rate limit events
    this.requestQueue = []; // Queue for managing request flow
    this.currentStrategy = 'balanced'; // balanced, conservative, aggressive, adaptive
    
    // System settings with intelligent defaults
    this.settings = {
      // Rate limiting detection and prediction
      maxRetries: 3,
      baseDelay: 1000, // Base delay between requests (ms)
      exponentialBackoff: true,
      predictiveThreshold: 0.85, // Switch when 85% of predicted limit reached
      cooldownPeriod: 90000, // 1.5 minutes cooldown after rate limit
      
      // Monitoring and analysis
      monitoringInterval: 15000, // Check stats every 15 seconds
      requestWindowSize: 100, // Track last 100 requests per endpoint
      analysisWindowHours: 24, // Analyze patterns over 24 hours
      
      // API switching logic
      healthCheckInterval: 30000, // Health check every 30 seconds
      failureThreshold: 3, // Switch after 3 consecutive failures
      recoveryThreshold: 5, // Consider recovered after 5 consecutive successes
      
      // Logging and reporting
      logDetailLevel: 'normal', // minimal, normal, detailed, debug
      persistData: true, // Save data to Chrome storage
      maxHistoryEntries: 1000, // Maximum history entries to keep
      
      // Advanced features
      adaptiveStrategy: true, // Automatically adjust strategy based on patterns
      predictiveAnalysis: true, // Use ML-like prediction for rate limits
      loadBalancing: true, // Distribute load across available APIs
      emergencyMode: false // Emergency mode for critical situations
    };

    // Performance metrics
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      rateLimitHits: 0,
      averageResponseTime: 0,
      uptime: Date.now(),
      lastOptimization: Date.now(),
      strategySwitches: 0,
      emergencyActivations: 0
    };

    // API endpoint categories
    this.endpointCategories = {
      'predefinedOffers/game': { type: 'data_fetch', priority: 'high', rateLimit: 60 },
      'predefinedOffers/me': { type: 'user_data', priority: 'medium', rateLimit: 30 },
      'predefinedOffersUser/me': { type: 'price_update', priority: 'critical', rateLimit: 20 },
      'predefinedOffers/details': { type: 'fallback_update', priority: 'medium', rateLimit: 15 }
    };

    // Initialize system
    this.initialize();
  }

  // ═══════════════════════════════════════════════════════════════════════════════
  // INITIALIZATION AND SETUP
  // ═══════════════════════════════════════════════════════════════════════════════

  async initialize() {
    console.log('🚀 Initializing Smart Anti-Limit System...');
    
    // Load saved data
    await this.loadPersistedData();
    
    // Start monitoring systems
    this.startHealthMonitoring();
    this.startPerformanceAnalysis();
    this.startAdaptiveOptimization();
    
    // Initialize default endpoints
    this.initializeDefaultEndpoints();
    
    console.log('✅ Smart Anti-Limit System initialized successfully');
    this.logSystem('System initialized with strategy: ' + this.currentStrategy, 'info');
  }

  initializeDefaultEndpoints() {
    const baseUrl = location.origin;
    const defaultEndpoints = [
      { url: `${baseUrl}/api/predefinedOffers/game`, type: 'primary', category: 'data_fetch' },
      { url: `${baseUrl}/api/predefinedOffers/me`, type: 'primary', category: 'user_data' },
      { url: `${baseUrl}/api/predefinedOffersUser/me`, type: 'primary', category: 'price_update' },
      { url: `${baseUrl}/api/predefinedOffers/details`, type: 'fallback', category: 'fallback_update' }
    ];

    defaultEndpoints.forEach(endpoint => {
      this.registerEndpoint(endpoint.url, endpoint.type, endpoint.category);
    });
  }

  // ═══════════════════════════════════════════════════════════════════════════════
  // ENDPOINT MANAGEMENT
  // ═══════════════════════════════════════════════════════════════════════════════

  registerEndpoint(url, type = 'primary', category = 'general') {
    const endpointKey = this.getEndpointKey(url);
    
    if (!this.apiEndpoints.has(endpointKey)) {
      const categoryInfo = this.endpointCategories[category] || { 
        type: 'general', 
        priority: 'medium', 
        rateLimit: 30 
      };

      this.apiEndpoints.set(endpointKey, {
        url,
        type, // 'primary', 'fallback', 'backup'
        category,
        categoryInfo,
        
        // Request statistics
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        rateLimitHits: 0,
        
        // Timing and performance
        averageResponseTime: 0,
        responseTimes: [],
        lastRequestTime: null,
        lastSuccessTime: null,
        lastFailureTime: null,
        
        // Health and status
        isHealthy: true,
        isActive: true,
        consecutiveSuccesses: 0,
        consecutiveFailures: 0,
        
        // Rate limiting
        lastRateLimit: null,
        cooldownUntil: null,
        estimatedRateLimit: categoryInfo.rateLimit,
        currentRequestRate: 0,
        
        // Advanced metrics
        errorTypes: new Map(),
        hourlyStats: new Map(),
        dailyStats: new Map(),
        requestPattern: [],
        
        // Prediction data
        rateLimitPrediction: null,
        healthScore: 100,
        reliabilityScore: 100,
        
        // Timestamps
        createdAt: Date.now(),
        lastUpdated: Date.now()
      });

      this.logSystem(`Registered endpoint: ${endpointKey} (${type}, ${category})`, 'info');
    }

    return this.apiEndpoints.get(endpointKey);
  }

  getEndpointKey(url) {
    // Extract meaningful part of URL for grouping similar endpoints
    try {
      const urlObj = new URL(url);
      const pathParts = urlObj.pathname.split('/').filter(part => part && !part.match(/^\d+$/));
      return pathParts.slice(-2).join('/'); // Last 2 meaningful path parts
    } catch {
      return url;
    }
  }

  // ═══════════════════════════════════════════════════════════════════════════════
  // REQUEST MONITORING AND RECORDING
  // ═══════════════════════════════════════════════════════════════════════════════

  recordRequest(url, success, responseTime, statusCode = null, errorType = null, responseData = null) {
    const endpointKey = this.getEndpointKey(url);
    const endpoint = this.apiEndpoints.get(endpointKey) || this.registerEndpoint(url);
    const now = Date.now();

    // Update basic statistics
    endpoint.totalRequests++;
    endpoint.lastRequestTime = now;
    endpoint.lastUpdated = now;
    this.metrics.totalRequests++;

    // Record response time
    if (responseTime > 0) {
      endpoint.responseTimes.push({ time: responseTime, timestamp: now });
      if (endpoint.responseTimes.length > this.settings.requestWindowSize) {
        endpoint.responseTimes.shift();
      }
      
      // Update average response time
      const recentTimes = endpoint.responseTimes.slice(-20).map(r => r.time);
      endpoint.averageResponseTime = recentTimes.reduce((a, b) => a + b, 0) / recentTimes.length;
    }

    if (success) {
      this.recordSuccess(endpoint, now, responseData);
    } else {
      this.recordFailure(endpoint, now, statusCode, errorType);
    }

    // Update request pattern for analysis
    this.updateRequestPattern(endpoint, now, success, statusCode);
    
    // Update health scores
    this.updateHealthScores(endpoint);
    
    // Check for rate limit prediction
    if (this.settings.predictiveAnalysis) {
      this.updateRateLimitPrediction(endpoint);
    }

    // Persist data periodically
    if (this.settings.persistData && this.metrics.totalRequests % 10 === 0) {
      this.persistData();
    }
  }

  recordSuccess(endpoint, timestamp, responseData) {
    endpoint.successfulRequests++;
    endpoint.lastSuccessTime = timestamp;
    endpoint.consecutiveSuccesses++;
    endpoint.consecutiveFailures = 0;
    this.metrics.successfulRequests++;

    // Recovery from unhealthy state
    if (!endpoint.isHealthy && endpoint.consecutiveSuccesses >= this.settings.recoveryThreshold) {
      endpoint.isHealthy = true;
      endpoint.cooldownUntil = null;
      this.logSystem(`✅ Endpoint ${endpoint.url} recovered (${endpoint.consecutiveSuccesses} consecutive successes)`, 'success');
    }
  }

  recordFailure(endpoint, timestamp, statusCode, errorType) {
    endpoint.failedRequests++;
    endpoint.lastFailureTime = timestamp;
    endpoint.consecutiveFailures++;
    endpoint.consecutiveSuccesses = 0;

    // Track error types
    if (errorType) {
      const count = endpoint.errorTypes.get(errorType) || 0;
      endpoint.errorTypes.set(errorType, count + 1);
    }

    // Handle rate limiting (429)
    if (statusCode === 429) {
      this.handleRateLimit(endpoint, timestamp);
    }
    
    // Mark as unhealthy if too many consecutive failures
    if (endpoint.consecutiveFailures >= this.settings.failureThreshold) {
      endpoint.isHealthy = false;
      this.logSystem(`⚠️ Endpoint ${endpoint.url} marked unhealthy (${endpoint.consecutiveFailures} consecutive failures)`, 'warning');
    }
  }

  handleRateLimit(endpoint, timestamp) {
    endpoint.rateLimitHits++;
    endpoint.lastRateLimit = timestamp;
    endpoint.cooldownUntil = timestamp + this.settings.cooldownPeriod;
    endpoint.isHealthy = false;
    this.metrics.rateLimitHits++;

    // Add to rate limit history
    this.rateLimitHistory.push({
      endpoint: endpoint.url,
      endpointKey: this.getEndpointKey(endpoint.url),
      timestamp,
      type: endpoint.type,
      category: endpoint.category,
      consecutiveRequests: endpoint.totalRequests,
      strategy: this.currentStrategy
    });

    // Trigger emergency mode if too many rate limits
    const recentRateLimits = this.rateLimitHistory.filter(rl => 
      timestamp - rl.timestamp < 300000 // Last 5 minutes
    ).length;

    if (recentRateLimits >= 3 && !this.settings.emergencyMode) {
      this.activateEmergencyMode();
    }

    this.logSystem(`🚫 Rate limit hit on ${endpoint.url} (cooldown until ${new Date(endpoint.cooldownUntil).toLocaleTimeString()})`, 'error');
  }

  // ═══════════════════════════════════════════════════════════════════════════════
  // SMART API SELECTION AND SWITCHING
  // ═══════════════════════════════════════════════════════════════════════════════

  getBestEndpoint(category, preferredType = 'primary') {
    const categoryEndpoints = Array.from(this.apiEndpoints.values())
      .filter(ep => ep.category === category && ep.isActive);

    if (categoryEndpoints.length === 0) {
      this.logSystem(`⚠️ No active endpoints found for category: ${category}`, 'warning');
      return null;
    }

    const now = Date.now();

    // Filter out endpoints in cooldown
    const availableEndpoints = categoryEndpoints.filter(ep =>
      !ep.cooldownUntil || now >= ep.cooldownUntil
    );

    if (availableEndpoints.length === 0) {
      // All endpoints are in cooldown, find the one that recovers soonest
      const soonestRecovery = categoryEndpoints.reduce((min, ep) =>
        !min || (ep.cooldownUntil && ep.cooldownUntil < min.cooldownUntil) ? ep : min
      );

      const waitTime = Math.max(0, soonestRecovery.cooldownUntil - now);
      this.logSystem(`⏳ All ${category} endpoints in cooldown. Next available in ${Math.round(waitTime/1000)}s`, 'info');
      return { endpoint: soonestRecovery, waitTime };
    }

    // Strategy-based selection
    let selectedEndpoint;

    switch (this.currentStrategy) {
      case 'conservative':
        selectedEndpoint = this.selectConservativeEndpoint(availableEndpoints, preferredType);
        break;
      case 'aggressive':
        selectedEndpoint = this.selectAggressiveEndpoint(availableEndpoints, preferredType);
        break;
      case 'adaptive':
        selectedEndpoint = this.selectAdaptiveEndpoint(availableEndpoints, preferredType);
        break;
      case 'balanced':
      default:
        selectedEndpoint = this.selectBalancedEndpoint(availableEndpoints, preferredType);
        break;
    }

    this.logSystem(`🎯 Selected ${selectedEndpoint.type} endpoint for ${category}: ${selectedEndpoint.url}`, 'debug');
    return { endpoint: selectedEndpoint, waitTime: 0 };
  }

  selectBalancedEndpoint(endpoints, preferredType) {
    // Prefer healthy endpoints of the preferred type
    const healthyPreferred = endpoints.filter(ep => ep.isHealthy && ep.type === preferredType);
    if (healthyPreferred.length > 0) {
      return this.selectByPerformance(healthyPreferred);
    }

    // Fall back to any healthy endpoint
    const healthy = endpoints.filter(ep => ep.isHealthy);
    if (healthy.length > 0) {
      return this.selectByPerformance(healthy);
    }

    // Last resort: least problematic endpoint
    return endpoints.reduce((best, ep) =>
      ep.consecutiveFailures < best.consecutiveFailures ? ep : best
    );
  }

  selectConservativeEndpoint(endpoints, preferredType) {
    // Prioritize endpoints with lowest failure rate and longest success streak
    return endpoints
      .filter(ep => ep.isHealthy)
      .sort((a, b) => {
        const aFailureRate = a.totalRequests > 0 ? a.failedRequests / a.totalRequests : 0;
        const bFailureRate = b.totalRequests > 0 ? b.failedRequests / b.totalRequests : 0;

        if (aFailureRate !== bFailureRate) return aFailureRate - bFailureRate;
        return b.consecutiveSuccesses - a.consecutiveSuccesses;
      })[0] || endpoints[0];
  }

  selectAggressiveEndpoint(endpoints, preferredType) {
    // Always prefer primary endpoints, even if they have some issues
    const primaryEndpoints = endpoints.filter(ep => ep.type === 'primary');
    if (primaryEndpoints.length > 0) {
      return this.selectByPerformance(primaryEndpoints);
    }
    return this.selectByPerformance(endpoints);
  }

  selectAdaptiveEndpoint(endpoints, preferredType) {
    // Use machine learning-like approach based on recent performance
    const now = Date.now();
    const recentWindow = 3600000; // 1 hour

    const scoredEndpoints = endpoints.map(ep => {
      let score = ep.healthScore;

      // Boost score for preferred type
      if (ep.type === preferredType) score += 20;

      // Recent performance weight
      const recentRequests = ep.requestPattern.filter(r => now - r.timestamp < recentWindow);
      if (recentRequests.length > 0) {
        const recentSuccessRate = recentRequests.filter(r => r.success).length / recentRequests.length;
        score += recentSuccessRate * 30;
      }

      // Response time factor
      if (ep.averageResponseTime > 0) {
        score -= Math.min(ep.averageResponseTime / 100, 20); // Penalty for slow responses
      }

      // Rate limit penalty
      if (ep.lastRateLimit && now - ep.lastRateLimit < 1800000) { // 30 minutes
        score -= 40;
      }

      return { endpoint: ep, score };
    });

    return scoredEndpoints.sort((a, b) => b.score - a.score)[0].endpoint;
  }

  selectByPerformance(endpoints) {
    if (endpoints.length === 1) return endpoints[0];

    // Select based on combined performance metrics
    return endpoints.reduce((best, ep) => {
      const bestScore = this.calculatePerformanceScore(best);
      const epScore = this.calculatePerformanceScore(ep);
      return epScore > bestScore ? ep : best;
    });
  }

  calculatePerformanceScore(endpoint) {
    let score = 100;

    // Success rate (0-40 points)
    if (endpoint.totalRequests > 0) {
      const successRate = endpoint.successfulRequests / endpoint.totalRequests;
      score += successRate * 40;
    }

    // Response time (0-20 points, inverted)
    if (endpoint.averageResponseTime > 0) {
      score += Math.max(0, 20 - (endpoint.averageResponseTime / 100));
    }

    // Consecutive successes (0-20 points)
    score += Math.min(endpoint.consecutiveSuccesses * 2, 20);

    // Rate limit penalty (-50 points if recent)
    const now = Date.now();
    if (endpoint.lastRateLimit && now - endpoint.lastRateLimit < 600000) { // 10 minutes
      score -= 50;
    }

    return score;
  }

  // ═══════════════════════════════════════════════════════════════════════════════
  // MONITORING AND HEALTH CHECKS
  // ═══════════════════════════════════════════════════════════════════════════════

  startHealthMonitoring() {
    setInterval(() => {
      this.performHealthCheck();
    }, this.settings.healthCheckInterval);
  }

  startPerformanceAnalysis() {
    setInterval(() => {
      this.analyzePerformance();
    }, this.settings.monitoringInterval);
  }

  startAdaptiveOptimization() {
    if (this.settings.adaptiveStrategy) {
      setInterval(() => {
        this.optimizeStrategy();
      }, 300000); // Every 5 minutes
    }
  }

  performHealthCheck() {
    const now = Date.now();
    let recoveredEndpoints = 0;

    for (const endpoint of this.apiEndpoints.values()) {
      // Check for cooldown recovery
      if (endpoint.cooldownUntil && now >= endpoint.cooldownUntil) {
        endpoint.cooldownUntil = null;
        if (endpoint.consecutiveFailures < this.settings.failureThreshold) {
          endpoint.isHealthy = true;
          recoveredEndpoints++;
          this.logSystem(`🔄 Endpoint recovered from cooldown: ${endpoint.url}`, 'info');
        }
      }

      // Update health scores
      this.updateHealthScores(endpoint);
    }

    if (recoveredEndpoints > 0) {
      this.logSystem(`✅ ${recoveredEndpoints} endpoints recovered during health check`, 'success');
    }

    // Check if we can exit emergency mode
    if (this.settings.emergencyMode) {
      this.checkEmergencyModeExit();
    }
  }

  updateHealthScores(endpoint) {
    const now = Date.now();
    let healthScore = 100;
    let reliabilityScore = 100;

    // Calculate health score based on recent performance
    if (endpoint.totalRequests > 0) {
      const successRate = endpoint.successfulRequests / endpoint.totalRequests;
      healthScore = successRate * 100;

      // Penalty for recent failures
      if (endpoint.consecutiveFailures > 0) {
        healthScore -= endpoint.consecutiveFailures * 10;
      }

      // Bonus for consecutive successes
      healthScore += Math.min(endpoint.consecutiveSuccesses * 2, 20);

      // Rate limit penalty
      if (endpoint.lastRateLimit && now - endpoint.lastRateLimit < 1800000) { // 30 minutes
        healthScore -= 30;
      }
    }

    // Calculate reliability score based on consistency
    if (endpoint.responseTimes.length > 5) {
      const times = endpoint.responseTimes.slice(-20).map(r => r.time);
      const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
      const variance = times.reduce((sum, time) => sum + Math.pow(time - avgTime, 2), 0) / times.length;
      const stdDev = Math.sqrt(variance);

      // Lower variance = higher reliability
      reliabilityScore = Math.max(0, 100 - (stdDev / avgTime) * 100);
    }

    endpoint.healthScore = Math.max(0, Math.min(100, healthScore));
    endpoint.reliabilityScore = Math.max(0, Math.min(100, reliabilityScore));
  }

  // ═══════════════════════════════════════════════════════════════════════════════
  // PREDICTIVE ANALYSIS AND OPTIMIZATION
  // ═══════════════════════════════════════════════════════════════════════════════

  updateRateLimitPrediction(endpoint) {
    const now = Date.now();
    const recentRequests = endpoint.requestPattern.filter(r =>
      now - r.timestamp < 3600000 // Last hour
    );

    if (recentRequests.length < 10) return; // Need sufficient data

    // Calculate current request rate (requests per minute)
    const timeSpan = Math.max(1, (now - recentRequests[0].timestamp) / 60000);
    const currentRate = recentRequests.length / timeSpan;
    endpoint.currentRequestRate = currentRate;

    // Predict when rate limit might be hit
    const estimatedLimit = endpoint.estimatedRateLimit;
    if (currentRate > 0 && currentRate < estimatedLimit) {
      const timeToLimit = (estimatedLimit - currentRate) / currentRate * 60000; // ms
      endpoint.rateLimitPrediction = {
        timeToLimit,
        confidence: Math.min(recentRequests.length / 50, 1), // Higher confidence with more data
        currentRate,
        estimatedLimit,
        timestamp: now
      };
    }
  }

  isPredictedRateLimit(endpoint) {
    if (!endpoint.rateLimitPrediction) return false;

    const prediction = endpoint.rateLimitPrediction;
    const timeSincePrediction = Date.now() - prediction.timestamp;

    // Prediction is stale
    if (timeSincePrediction > 300000) return false; // 5 minutes

    // Check if we're approaching the predicted limit
    const threshold = this.settings.predictiveThreshold;
    return prediction.currentRate >= (prediction.estimatedLimit * threshold);
  }

  analyzePerformance() {
    const now = Date.now();
    let totalHealthy = 0;
    let totalUnhealthy = 0;
    let avgResponseTime = 0;
    let totalResponseTimes = 0;

    for (const endpoint of this.apiEndpoints.values()) {
      if (endpoint.isHealthy) {
        totalHealthy++;
      } else {
        totalUnhealthy++;
      }

      if (endpoint.averageResponseTime > 0) {
        avgResponseTime += endpoint.averageResponseTime;
        totalResponseTimes++;
      }
    }

    // Update system metrics
    this.metrics.averageResponseTime = totalResponseTimes > 0 ?
      avgResponseTime / totalResponseTimes : 0;

    // Log performance summary periodically
    if (this.metrics.totalRequests % 100 === 0) {
      const successRate = this.metrics.totalRequests > 0 ?
        (this.metrics.successfulRequests / this.metrics.totalRequests * 100).toFixed(1) : 0;

      this.logSystem(`📊 Performance: ${successRate}% success, ${totalHealthy} healthy endpoints, avg ${Math.round(this.metrics.averageResponseTime)}ms`, 'info');
    }
  }

  optimizeStrategy() {
    const report = this.getStatusReport();
    const oldStrategy = this.currentStrategy;

    // Strategy optimization logic
    if (report.recentRateLimits > 5) {
      this.currentStrategy = 'conservative';
    } else if (report.recentRateLimits === 0 && report.healthyEndpoints === report.totalEndpoints) {
      this.currentStrategy = 'aggressive';
    } else if (this.settings.adaptiveStrategy) {
      this.currentStrategy = 'adaptive';
    } else {
      this.currentStrategy = 'balanced';
    }

    if (oldStrategy !== this.currentStrategy) {
      this.metrics.strategySwitches++;
      this.logSystem(`🔄 Strategy changed: ${oldStrategy} → ${this.currentStrategy}`, 'info');
    }
  }

  // ═══════════════════════════════════════════════════════════════════════════════
  // EMERGENCY MODE AND RECOVERY
  // ═══════════════════════════════════════════════════════════════════════════════

  activateEmergencyMode() {
    this.settings.emergencyMode = true;
    this.metrics.emergencyActivations++;

    // Increase delays and cooldowns
    this.settings.baseDelay *= 2;
    this.settings.cooldownPeriod *= 1.5;

    // Switch to most conservative strategy
    this.currentStrategy = 'conservative';

    this.logSystem('🚨 EMERGENCY MODE ACTIVATED - Implementing aggressive rate limiting protection', 'error');

    // Auto-recovery check in 10 minutes
    setTimeout(() => {
      this.checkEmergencyModeExit();
    }, 600000);
  }

  checkEmergencyModeExit() {
    const now = Date.now();
    const recentRateLimits = this.rateLimitHistory.filter(rl =>
      now - rl.timestamp < 600000 // Last 10 minutes
    ).length;

    if (recentRateLimits === 0) {
      this.deactivateEmergencyMode();
    }
  }

  deactivateEmergencyMode() {
    this.settings.emergencyMode = false;

    // Restore normal delays
    this.settings.baseDelay = Math.max(1000, this.settings.baseDelay / 2);
    this.settings.cooldownPeriod = Math.max(90000, this.settings.cooldownPeriod / 1.5);

    this.logSystem('✅ Emergency mode deactivated - Returning to normal operation', 'success');
  }

  // ═══════════════════════════════════════════════════════════════════════════════
  // UTILITY AND HELPER METHODS
  // ═══════════════════════════════════════════════════════════════════════════════

  updateRequestPattern(endpoint, timestamp, success, statusCode) {
    endpoint.requestPattern.push({
      timestamp,
      success,
      statusCode,
      strategy: this.currentStrategy
    });

    // Keep only recent patterns
    const cutoff = timestamp - 86400000; // 24 hours
    endpoint.requestPattern = endpoint.requestPattern.filter(p => p.timestamp > cutoff);
  }

  getStatusReport() {
    const report = {
      strategy: this.currentStrategy,
      emergencyMode: this.settings.emergencyMode,
      totalEndpoints: this.apiEndpoints.size,
      healthyEndpoints: 0,
      rateLimitedEndpoints: 0,
      recentRateLimits: 0,
      averageHealthScore: 0,
      systemUptime: Date.now() - this.metrics.uptime,
      recommendations: []
    };

    const now = Date.now();
    const oneHourAgo = now - 3600000;
    let totalHealthScore = 0;

    for (const endpoint of this.apiEndpoints.values()) {
      if (endpoint.isHealthy) report.healthyEndpoints++;
      if (endpoint.cooldownUntil && now < endpoint.cooldownUntil) report.rateLimitedEndpoints++;

      totalHealthScore += endpoint.healthScore;

      // Count recent rate limits
      if (endpoint.lastRateLimit && endpoint.lastRateLimit > oneHourAgo) {
        report.recentRateLimits++;
      }
    }

    report.averageHealthScore = this.apiEndpoints.size > 0 ?
      totalHealthScore / this.apiEndpoints.size : 0;

    // Generate recommendations
    this.generateRecommendations(report);

    return report;
  }

  generateRecommendations(report) {
    if (report.recentRateLimits > 3) {
      report.recommendations.push('Consider increasing delays between requests');
    }

    if (report.healthyEndpoints < report.totalEndpoints * 0.5) {
      report.recommendations.push('Multiple endpoints unhealthy - check network connectivity');
    }

    if (report.averageHealthScore < 70) {
      report.recommendations.push('System performance degraded - consider reducing request frequency');
    }

    if (this.settings.emergencyMode) {
      report.recommendations.push('Emergency mode active - system will auto-recover when stable');
    }
  }

  logSystem(message, level = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const logMessage = `[AntiLimit ${timestamp}] ${message}`;

    // Use different console methods based on level
    switch (level) {
      case 'error':
        console.error(logMessage);
        break;
      case 'warning':
        console.warn(logMessage);
        break;
      case 'success':
        console.log(`%c${logMessage}`, 'color: green');
        break;
      case 'debug':
        if (this.settings.logDetailLevel === 'debug') {
          console.debug(logMessage);
        }
        break;
      default:
        console.log(logMessage);
    }

    // Also log to extension's logging system if available
    if (typeof addToEnhancedLog === 'function') {
      addToEnhancedLog(message, level === 'error' ? 'error' : level === 'warning' ? 'warning' : 'info');
    }
  }

  // ═══════════════════════════════════════════════════════════════════════════════
  // DATA PERSISTENCE
  // ═══════════════════════════════════════════════════════════════════════════════

  async persistData() {
    if (!this.settings.persistData) return;

    const data = {
      settings: this.settings,
      currentStrategy: this.currentStrategy,
      metrics: this.metrics,
      rateLimitHistory: this.rateLimitHistory.slice(-this.settings.maxHistoryEntries),
      endpoints: Array.from(this.apiEndpoints.entries()).map(([key, endpoint]) => [
        key,
        {
          ...endpoint,
          // Only persist essential data to avoid storage bloat
          responseTimes: endpoint.responseTimes.slice(-20),
          requestPattern: endpoint.requestPattern.slice(-100),
          errorTypes: Array.from(endpoint.errorTypes.entries())
        }
      ]),
      lastSaved: Date.now()
    };

    try {
      await new Promise((resolve, reject) => {
        chrome.storage.local.set({ smartAntiLimitData: data }, () => {
          if (chrome.runtime.lastError) {
            reject(chrome.runtime.lastError);
          } else {
            resolve();
          }
        });
      });
    } catch (error) {
      this.logSystem(`Failed to persist data: ${error.message}`, 'error');
    }
  }

  async loadPersistedData() {
    if (!this.settings.persistData) return;

    try {
      const result = await new Promise((resolve) => {
        chrome.storage.local.get('smartAntiLimitData', resolve);
      });

      if (result.smartAntiLimitData) {
        const data = result.smartAntiLimitData;

        // Restore settings
        this.settings = { ...this.settings, ...data.settings };
        this.currentStrategy = data.currentStrategy || 'balanced';
        this.metrics = { ...this.metrics, ...data.metrics };
        this.rateLimitHistory = data.rateLimitHistory || [];

        // Restore endpoints
        if (data.endpoints) {
          for (const [key, endpointData] of data.endpoints) {
            this.apiEndpoints.set(key, {
              ...endpointData,
              errorTypes: new Map(endpointData.errorTypes || [])
            });
          }
        }

        this.logSystem('Loaded persisted anti-limit data', 'info');
      }
    } catch (error) {
      this.logSystem(`Failed to load persisted data: ${error.message}`, 'error');
    }
  }

  // ═══════════════════════════════════════════════════════════════════════════════
  // PUBLIC API METHODS
  // ═══════════════════════════════════════════════════════════════════════════════

  // Main method for making smart API requests
  async makeSmartRequest(url, options = {}, category = 'general') {
    const endpointKey = this.getEndpointKey(url);
    const startTime = Date.now();

    try {
      // Get best endpoint for this category
      const selection = this.getBestEndpoint(category);

      if (!selection || !selection.endpoint) {
        throw new Error(`No available endpoints for category: ${category}`);
      }

      const { endpoint, waitTime } = selection;

      // Wait if necessary
      if (waitTime > 0) {
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }

      // Add delay based on current strategy
      const delay = this.calculateRequestDelay(endpoint);
      if (delay > 0) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }

      // Make the request
      const response = await fetch(url, {
        ...options,
        credentials: options.credentials || 'include'
      });

      const responseTime = Date.now() - startTime;
      const success = response.ok;

      // Record the request
      this.recordRequest(url, success, responseTime, response.status,
        success ? null : 'HTTP_ERROR', null);

      return response;

    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.recordRequest(url, false, responseTime, null, 'NETWORK_ERROR', null);
      throw error;
    }
  }

  calculateRequestDelay(endpoint) {
    let delay = this.settings.baseDelay;

    // Increase delay based on recent failures
    if (endpoint.consecutiveFailures > 0) {
      delay *= Math.pow(2, Math.min(endpoint.consecutiveFailures, 5));
    }

    // Emergency mode increases delays
    if (this.settings.emergencyMode) {
      delay *= 3;
    }

    // Add some randomization to avoid thundering herd
    delay += Math.random() * 1000;

    return Math.min(delay, 30000); // Cap at 30 seconds
  }

  // Get current system status for dashboard
  getDashboardData() {
    const report = this.getStatusReport();
    const endpoints = Array.from(this.apiEndpoints.values()).map(ep => ({
      url: ep.url,
      type: ep.type,
      category: ep.category,
      isHealthy: ep.isHealthy,
      healthScore: Math.round(ep.healthScore),
      totalRequests: ep.totalRequests,
      successRate: ep.totalRequests > 0 ?
        Math.round((ep.successfulRequests / ep.totalRequests) * 100) : 0,
      rateLimitHits: ep.rateLimitHits,
      averageResponseTime: Math.round(ep.averageResponseTime),
      lastRateLimit: ep.lastRateLimit,
      cooldownUntil: ep.cooldownUntil,
      consecutiveSuccesses: ep.consecutiveSuccesses,
      consecutiveFailures: ep.consecutiveFailures
    }));

    return {
      ...report,
      endpoints,
      settings: this.settings,
      metrics: this.metrics,
      rateLimitHistory: this.rateLimitHistory.slice(-50) // Last 50 events
    };
  }

  // Update settings from dashboard
  updateSettings(newSettings) {
    this.settings = { ...this.settings, ...newSettings };
    this.logSystem('Settings updated from dashboard', 'info');
    this.persistData();
  }

  // Manual strategy change
  setStrategy(strategy) {
    const oldStrategy = this.currentStrategy;
    this.currentStrategy = strategy;
    this.metrics.strategySwitches++;
    this.logSystem(`Strategy manually changed: ${oldStrategy} → ${strategy}`, 'info');
    this.persistData();
  }

  // Reset endpoint health
  resetEndpointHealth(endpointKey) {
    const endpoint = this.apiEndpoints.get(endpointKey);
    if (endpoint) {
      endpoint.isHealthy = true;
      endpoint.cooldownUntil = null;
      endpoint.consecutiveFailures = 0;
      endpoint.rateLimitHits = 0;
      this.logSystem(`Manually reset health for endpoint: ${endpointKey}`, 'info');
    }
  }

  // ═══════════════════════════════════════════════════════════════════════════════
  // TESTING AND VALIDATION
  // ═══════════════════════════════════════════════════════════════════════════════

  // Test the anti-limit system with simulated scenarios
  runSystemTest() {
    this.logSystem('🧪 Starting Smart Anti-Limit System test...', 'info');

    // Test 1: Register test endpoints
    const testEndpoint1 = 'https://test.api/endpoint1';
    const testEndpoint2 = 'https://test.api/endpoint2';

    this.registerEndpoint(testEndpoint1, 'primary', 'data_fetch');
    this.registerEndpoint(testEndpoint2, 'fallback', 'data_fetch');

    // Test 2: Simulate successful requests
    for (let i = 0; i < 5; i++) {
      this.recordRequest(testEndpoint1, true, 100 + Math.random() * 50, 200, null);
    }

    // Test 3: Simulate rate limiting
    this.recordRequest(testEndpoint1, false, 0, 429, 'RATE_LIMIT');

    // Test 4: Check endpoint selection
    const selection = this.getBestEndpoint('data_fetch', 'primary');

    // Test 5: Validate dashboard data
    const dashboardData = this.getDashboardData();

    // Test results
    const testResults = {
      endpointsRegistered: this.apiEndpoints.size >= 2,
      rateLimitDetected: this.rateLimitHistory.length > 0,
      endpointSwitching: selection && selection.endpoint === testEndpoint2,
      dashboardData: dashboardData && dashboardData.endpoints.length >= 2,
      systemHealthy: this.metrics.totalRequests > 0
    };

    const allTestsPassed = Object.values(testResults).every(result => result === true);

    this.logSystem(`🧪 Test Results: ${allTestsPassed ? '✅ ALL PASSED' : '❌ SOME FAILED'}`,
      allTestsPassed ? 'success' : 'error');

    if (!allTestsPassed) {
      this.logSystem(`Failed tests: ${Object.entries(testResults)
        .filter(([, passed]) => !passed)
        .map(([test]) => test)
        .join(', ')}`, 'error');
    }

    return { allTestsPassed, testResults };
  }

  // Validate system configuration
  validateConfiguration() {
    const issues = [];

    // Check settings
    if (this.settings.baseDelay < 100) {
      issues.push('Base delay too low (< 100ms)');
    }

    if (this.settings.cooldownPeriod < 30000) {
      issues.push('Cooldown period too short (< 30s)');
    }

    if (this.settings.predictiveThreshold < 0.5 || this.settings.predictiveThreshold > 1) {
      issues.push('Predictive threshold out of range (0.5-1.0)');
    }

    // Check endpoint categories
    const requiredCategories = ['data_fetch', 'user_data', 'price_update'];
    const availableCategories = new Set(
      Array.from(this.apiEndpoints.values()).map(ep => ep.category)
    );

    for (const category of requiredCategories) {
      if (!availableCategories.has(category)) {
        issues.push(`Missing endpoint category: ${category}`);
      }
    }

    if (issues.length > 0) {
      this.logSystem(`⚠️ Configuration issues found: ${issues.join(', ')}`, 'warning');
      return false;
    }

    this.logSystem('✅ Configuration validation passed', 'success');
    return true;
  }

  // Performance benchmark
  async runPerformanceBenchmark() {
    this.logSystem('🏃 Running performance benchmark...', 'info');

    const startTime = Date.now();
    const iterations = 1000;

    // Benchmark endpoint selection
    for (let i = 0; i < iterations; i++) {
      this.getBestEndpoint('data_fetch', 'primary');
    }

    const endpointSelectionTime = Date.now() - startTime;

    // Benchmark request recording
    const recordingStartTime = Date.now();
    for (let i = 0; i < iterations; i++) {
      this.recordRequest('test://benchmark', true, 100, 200, null);
    }
    const recordingTime = Date.now() - recordingStartTime;

    // Benchmark dashboard data generation
    const dashboardStartTime = Date.now();
    for (let i = 0; i < 100; i++) {
      this.getDashboardData();
    }
    const dashboardTime = Date.now() - dashboardStartTime;

    const results = {
      endpointSelectionAvg: endpointSelectionTime / iterations,
      requestRecordingAvg: recordingTime / iterations,
      dashboardGenerationAvg: dashboardTime / 100,
      totalTime: Date.now() - startTime
    };

    this.logSystem(`🏃 Benchmark Results:
      - Endpoint selection: ${results.endpointSelectionAvg.toFixed(3)}ms avg
      - Request recording: ${results.requestRecordingAvg.toFixed(3)}ms avg
      - Dashboard generation: ${results.dashboardGenerationAvg.toFixed(3)}ms avg
      - Total time: ${results.totalTime}ms`, 'info');

    return results;
  }
}

// Export for use in content script
if (typeof window !== 'undefined') {
  window.SmartAntiLimitSystem = SmartAntiLimitSystem;
}
