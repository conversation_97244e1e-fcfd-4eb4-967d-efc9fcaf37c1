// This will be the home of our new Rate Limit Manager module.
// It will manage API request timings dynamically to avoid hitting rate limits.

const RateLimitManager = (() => {
  const state = {
    apis: {
      primary: {
        currentDelay: 2000, // ms
        lastRateLimitTime: 0,
        requestHistory: [], // { timestamp, status }
      },
      fallback: {
        currentDelay: 5000, // ms
        lastRateLimitTime: 0,
        requestHistory: [],
      },
    },
    settings: {
      primary: {
        baselineDelay: 2000,
        backoffFactor: 3,
        cooldownFactor: 0.9,
        maxDelay: 300000,
      },
      fallback: {
        baselineDelay: 5000,
        backoffFactor: 3,
        cooldownFactor: 0.9,
        maxDelay: 300000,
      },
      historyWindow: 60000, // 1 minute window for history
    },
    log: [],
  };

  function log(message) {
    const timestamp = new Date().toISOString();
    state.log.unshift({ timestamp, message });
    if (state.log.length > 100) {
      state.log.pop();
    }
    // Optional: Add a UI update hook here later
  }

  function getDelay(apiType) {
    if (!state.apis[apiType]) {
      log(`Error: Unknown API type "${apiType}"`);
      return state.settings.primary.baselineDelay; // Return a default
    }
    return state.apis[apiType].currentDelay;
  }

  function recordSuccess(apiType) {
    if (!state.apis[apiType]) return;

    const apiState = state.apis[apiType];
    const settings = state.settings[apiType];
    const now = Date.now();

    // Add to history
    apiState.requestHistory.unshift({ timestamp: now, status: 'success' });

    // Prune old history
    apiState.requestHistory = apiState.requestHistory.filter(
      (req) => now - req.timestamp < state.settings.historyWindow
    );

    // Cooldown: Gradually decrease delay towards baseline
    const newDelay = Math.max(
      apiState.currentDelay * settings.cooldownFactor,
      settings.baselineDelay
    );

    if (newDelay !== apiState.currentDelay) {
      log(`${apiType} API cooldown. Delay: ${(apiState.currentDelay / 1000).toFixed(2)}s -> ${(newDelay / 1000).toFixed(2)}s`);
      apiState.currentDelay = newDelay;
    }
  }

  function recordFailure(apiType, statusCode) {
    if (!state.apis[apiType]) return;

    const apiState = state.apis[apiType];
    const settings = state.settings[apiType];
    const now = Date.now();

    // Add to history
    apiState.requestHistory.unshift({ timestamp: now, status: `failed_${statusCode}` });

    // Prune old history (same as success)
    apiState.requestHistory = apiState.requestHistory.filter(
        (req) => now - req.timestamp < state.settings.historyWindow
    );

    if (statusCode === 429) { // Rate limit detected
      apiState.lastRateLimitTime = now;
      const newDelay = Math.min(
        apiState.currentDelay * settings.backoffFactor,
        settings.maxDelay
      );
      log(`RATE LIMIT on ${apiType} API. Backing off. Delay: ${(apiState.currentDelay / 1000).toFixed(2)}s -> ${(newDelay / 1000).toFixed(2)}s`);
      apiState.currentDelay = newDelay;
    } else {
      // For now, other failures don't affect timing, just recorded.
      log(`Non-rate-limit failure on ${apiType} API. Status: ${statusCode}`);
    }
  }

  function getState() {
    // Return a copy to prevent direct modification
    return JSON.parse(JSON.stringify(state));
  }

  function updateSettings(newSettings) {
    // Deep merge new settings
    for (const apiType in newSettings) {
      if (state.settings[apiType]) {
        for (const key in newSettings[apiType]) {
          if (state.settings[apiType][key] !== undefined) {
            state.settings[apiType][key] = newSettings[apiType][key];
          }
        }
      }
    }
     if (newSettings.historyWindow) {
        state.settings.historyWindow = newSettings.historyWindow;
    }
    log("RLM settings updated.");
  }


  // Public API
  return {
    getDelay,
    recordSuccess,
    recordFailure,
    getState,
    updateSettings,
  };
})(); 